ESX = exports['es_extended']:getSharedObject()
local ox_inventory = exports.ox_inventory

for k, v in pairs(Businesses) do
    if v.enabled and v.traysenabled then
        for trayName, trayData in pairs(v.trays) do
            for _, coords in pairs(trayData.coords) do
                exports.ox_target:addBoxZone({
                    coords = coords,
                    size = trayData.size,
                    rotation = trayData.rotation,
                    debug = Config.Debug,
                    options = {
                        {
                            name = trayData.label,
                            event = 'nukexd_businesses:opentargetstash',
                            id = trayName,
                            icon = trayData.icon,
                            label = "Open Tray",
                            data = {
                                label = trayData.label,
                                slots = trayData.slots,
                                weight = trayData.weight,
                                coords = coords
                            }
                        }
                    }
                })
            end
        end
    end
end

RegisterNetEvent('nukexd_businesses:opentargetstash', function(info)
    if info and info.data then
        local stashId = info.id
        local stashLabel = info.data.label
        local stashSlots = info.data.slots
        local stashWeight = info.data.weight
        local stashCoords = info.data.coords

        if ox_inventory:openInventory('stash', stashId) == false then
            TriggerServerEvent(
                'nukexd_businesses:registerstash',
                stashId,
                stashLabel,
                stashSlots,
                stashWeight,
                stashCoords
            )
            ox_inventory:openInventory('stash', stashId)
        end
    end
end)
