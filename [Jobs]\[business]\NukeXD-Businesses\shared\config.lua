Businesses = {}
prebuiltbars = {}
Config = {}

Config.Debug = false

-- Duty System 
Config.DutySystem = {
    enabled = true,
    offDutyPrefix = "off",
    notifications = {
        onDuty = "You are now on duty as %s",
        offDuty = "You are now off duty",
        noPermission = "You don't have permission to use this duty zone"
    }
}

Config.DutySystem.periodicZoneCheck = { -- Config to Override Snipe Job Menu! 
    enabled = true, 
    interval = 15, 
    triggerDutyLogger = true,
    showNotification = true, 
    notificationMessage = "You have been automatically clocked off for leaving the work zone." 
}