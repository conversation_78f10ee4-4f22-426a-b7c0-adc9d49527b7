ESX = exports['es_extended']:getSharedObject()
local ox_inventory = exports.ox_inventory
local zoneTable = {}
local showingUIStash = false
local PlayerJob = {}
local currentPointCoordsStash = nil
local canOpen = false

-- Update PlayerJob when player loads or job changes
RegisterNetEvent('esx:playerLoaded', function(xPlayer)
    PlayerJob = xPlayer.job
end)

RegisterNetEvent('esx:setJob', function(job)
    PlayerJob = job
end)

-- Safe fallback
CreateThread(function()
    while ESX.GetPlayerData().job == nil do
        Wait(100)
    end
    PlayerJob = ESX.GetPlayerData().job
end)

-- Handle entering stash zones
local function onEnterNearbyStash(point)
    if point.job ~= PlayerJob.name then
        canOpen = false
    else
        canOpen = true
    end

    -- Draw Marker
    if point.drawmarker then
        DrawMarker(25, point.coords.x, point.coords.y, point.coords.z - 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.45, 0.45, 0.45, 30, 150, 30, 222, false, false, 2, false, false, false, false)
    end

    -- Show/hide interaction UI
    if point.isClosest and point.currentDistance <= 0.7 and not showingUIStash then
        lib.showTextUI('[E] Open ' .. point.dataInfo.label)
        showingUIStash = true
        currentPointCoordsStash = point.coords
    elseif showingUIStash and point.currentDistance >= 0.7 and currentPointCoordsStash == point.coords then
        lib.hideTextUI()
        showingUIStash = false
        currentPointCoordsStash = nil
    end

    -- Handle interaction
    if point.isClosest and point.currentDistance <= 0.7 and IsControlJustReleased(0, 38) then
        if canOpen then
            if ox_inventory:openInventory('stash', point.dataInfo.label) == false then
                -- Stash doesn't exist, register then open
                TriggerServerEvent('nukexd_businesses:registerstash',
                    point.dataInfo.label,
                    point.dataInfo.label,
                    tonumber(point.dataInfo.slots),
                    tonumber(point.dataInfo.weight),
                    point.dataInfo.coords
                )
                Wait(250) -- Small delay to let server handle registration
                ox_inventory:openInventory('stash', point.dataInfo.label)
            end
        else
            lib.notify({
                description = 'You do not have permission to this stash.',
                type = 'error'
            })
        end
    end
end

-- Register stash zones from Businesses table
CreateThread(function()
    for k, v in pairs(Businesses) do
        if v.enabled and v.stash.enabled then
            for _, coords in pairs(v.stash.coords) do
                zoneTable[#zoneTable+1] = lib.points.new({
                    coords = coords,
                    distance = 10.0,
                    nearby = onEnterNearbyStash,
                    dataInfo = v.stash,
                    job = v.job,
                    drawmarker = v.drawmarker,
                })
            end
        end
    end
end)
