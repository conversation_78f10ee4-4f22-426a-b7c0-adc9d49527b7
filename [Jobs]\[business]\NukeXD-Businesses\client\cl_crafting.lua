ESX = exports['es_extended']:getSharedObject()
zoneTable = {}
showingUI = false
PlayerJob = {}
local items = exports.ox_inventory:Items()
local currentPointCoords = nil

-- Events for ESX player data
RegisterNetEvent('esx:playerLoaded', function(xPlayer)
    PlayerJob = xPlayer.job
end)

RegisterNetEvent('esx:setJob', function(job)
    PlayerJob = job
end)

-- Wait until player data is available before setting job
CreateThread(function()
    while ESX == nil do Wait(100) end
    while not ESX.IsPlayerLoaded() do Wait(100) end

    local playerData = ESX.GetPlayerData()
    if playerData and playerData.job then
        PlayerJob = playerData.job
    end
end)

-- Ingredient text generator
local function GetIngredientsText(ingredients)
    if not next(ingredients) then return "" end
    local text = ''
    for k, v in pairs(ingredients) do
        text = text .. items[k].label .. ' x' .. v .. '\n '
    end
    return text
end

-- Check if player has ingredients
local function CheckIfHasItems(ingredients, amount)
    if not next(ingredients) then return true end
    for k, v in pairs(ingredients) do
        local itemCount = exports.ox_inventory:GetItemCount(k)
        if v == 0 then
            if itemCount <= 0 then return false end
        elseif itemCount < (v * amount) then
            return false
        end
    end
    return true
end

-- Turn player toward entity
function lookEnt(entity)
    if type(entity) == "vector3" then
        if not IsPedHeadingTowardsPosition(PlayerPedId(), entity, 10.0) then
            TaskTurnPedToFaceCoord(PlayerPedId(), entity, 1500)
        end
    elseif DoesEntityExist(entity) then
        local coords = GetEntityCoords(entity)
        if not IsPedHeadingTowardsPosition(PlayerPedId(), coords, 30.0) then
            TaskTurnPedToFaceCoord(PlayerPedId(), coords, 1500)
        end
    end
end

-- Crafting UI interaction
local function DoAction(dataInfo)
    local options = {}
    for k, v in pairs(dataInfo.items) do
        local disabled = not CheckIfHasItems(v.ingredients, 1)
        options[#options + 1] = {
            title = items[v.item].label,
            description = GetIngredientsText(v.ingredients),
            image = items[v.item].client.image,
            disabled = disabled,
            onSelect = function()
                local input = lib.inputDialog('Crafting', {
                    { type = 'number', label = 'How many would you like to make?', default = 1, min = 1 },
                })
                if not input or not input[1] then return end

                local hasItems = CheckIfHasItems(v.ingredients, input[1])
                if hasItems then
                    if lib.progressCircle({
                        duration = dataInfo.craftingtime * input[1],
                        label = dataInfo.craftinglabel .. ' ' .. input[1] .. "x " .. items[v.item].label,
                        useWhileDead = false,
                        position = 'bottom',
                        canCancel = true,
                        anim = {
                            dict = dataInfo.anim.dict,
                            clip = dataInfo.anim.clip,
                            flaged = 49,
                        },
                        disable = {
                            move = true,
                            car = true,
                            mouse = false,
                        },
                    }) then
                        lib.callback.await('nukexd_businesses:craft', false, v.item, v.ingredients, input[1])
                    end
                else
                    lib.notify({
                        title = 'Not Enough Items',
                        description = 'You do not have enough items to craft this.',
                        type = 'error'
                    })
                end
            end
        }
    end

    lib.registerContext({
        id = 'crafting-businesses',
        title = dataInfo.label,
        options = options,
    })
    lib.showContext('crafting-businesses')
end

-- Nearby zone interaction
local function onEnterNearby(point)
    if not PlayerJob or not PlayerJob.name or point.job ~= PlayerJob.name then return end

    if point.drawmarker then
        DrawMarker(25, point.coords.x, point.coords.y, point.coords.z - 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
                   0.45, 0.45, 0.45, 30, 150, 30, 222, false, false, 2, false, false, false, false)
    end

    if point.isClosest and point.currentDistance < 0.7 and not showingUI then
        lib.showTextUI('[E] ' .. point.dataInfo.label)
        showingUI = true
        currentPointCoords = point.coords
    elseif showingUI and point.currentDistance > 0.7 and currentPointCoords == point.coords then
        lib.hideTextUI()
        showingUI = false
        currentPointCoords = nil
    end

    if point.isClosest and point.currentDistance < 0.7 and IsControlJustReleased(0, 38) then
        lookEnt(point.coords)
        DoAction(point.dataInfo)
    end
end

-- Setup crafting zones
CreateThread(function()
    while not ESX.IsPlayerLoaded() do Wait(100) end

    for k, v in pairs(Businesses) do
        if v.enabled then
            for station, stationData in pairs(v.crafting) do
                for _, coords in pairs(stationData.coords) do
                    zoneTable[#zoneTable + 1] = lib.points.new({
                        coords = coords,
                        distance = 10.0,
                        nearby = onEnterNearby,
                        dataInfo = stationData,
                        job = v.job,
                        drawmarker = v.drawmarker,
                    })
                end
            end
        end
    end
end)
