--- Bars & Foods

Businesses["catcafe"] = {
    enabled = true,
    drawmarker = true,
    job = "catcafe",
    dutyzone = {
        zones = {
            vector2(-591.***********, -1087.8620605469),
            vector2(-563.***********, -1087.8508300781),
            vector2(-563.***********, -1045.1898193359),
            vector2(-618.***********, -1044.2902832031),
            vector2(-617.***********, -1079.7291259766),
            vector2(-599.***********, -1079.6105957031)
        },
        autoClock = { enter = true, exit = true },
        label = "UWU Cafe",
    },
    blips = {
        enabled = true,
        label = "Cat Cafe",
        sprite = 89,
        color = 48,
        scale = 0.7,
        coords = vec3(-581.06, -1066.22, 22.34)
    },
    stash = {
        enabled = true,
        label = 'CatCafe Fridge',
        slots = '40',
        weight = '400000',
        coords = {
            vector3(-586.5927, -1059.6642, 22.2426)
        },
    },
    props = {
        enabled = false,
        prop = '',
        coords = {},
    },
    traysenabled = true,
    trays = {
        ["CatCafeTray1"] = {
            label = "CatCafe Tray 1",
            slots = '20',
            weight = '40000',
            coords = {
                vector3(-584.0424, -1059.2629, 22.4176)
            },
            size = vec3(0.5, 0.5, 0.5),
            rotation = 5,
            icon = 'fa-solid fa-bowl-food',
        },
        ["CatCafeTray2"] = {
            label = "CatCafe Tray 2",
            slots = '20',
            weight = '20000',
            coords = {
                vector3(-584.0706, -1062.0321, 22.4176)
            },
            size = vec3(0.5, 0.5, 0.5),
            rotation = 5,
            icon = 'fa-solid fa-bowl-food',
        },
        ["CatCafeTray3"] = {
            label = "CatCafe Tray 3",
            slots = '20',
            weight = '20000',
            coords = {
                vector3(-587.4568, -1059.6674, 22.2113)
            },
            size = vec3(1, 1.5, 0.7),
            rotation = 5,
            icon = 'fa-solid fa-bowl-food',
        },
        
    },
    crafting = {
        ["food"] = {
            label = "Food Station",
            craftinglabel = 'Cooking Food',
            craftingtime = '2000',
            coords = {
                vector3(-590.6306, -1059.1624, 22.4607)
            }, -- coordinates for food Station
            anim = {
                dict = "mp_ped_interaction",
                clip = "handshake_guy_a",
            },
            items = {
                [1] = {
                    item = "nekocookie",
                    ingredients = { ['flour'] = 1, ['milk'] = 1, }
                },
                [2]  = {
                    item = "nekodonut",
                    ingredients = { ['flour'] = 1, ['milk'] = 1, }
                },
                -- [3]  = {
                --     item = "cake",
                --     ingredients = { ['flour'] = 1, ['milk'] = 1, ['strawberry'] = 1, }
                -- },
                -- [4]  = {
                --     item = "cakepop",
                --     ingredients = { ['flour'] = 1, ['milk'] = 1, ['sugar'] = 1, }
                -- },
                -- [5]  = {
                --     item = "pancake",
                --     ingredients = { ['flour'] = 1, ['milk'] = 1, ['strawberry'] = 1, }
                -- },
                [3]  = {
                    item = "pizza",
                    ingredients =  { ['flour'] = 1, ['milk'] = 1,}
                },
            },
        },
        ["coffee"] = {
            label = "Pour Coffee",
            craftinglabel = 'Brewing Coffee',
            craftingtime = '2000',
            coords = {
                vector3(-586.23, -1061.89, 22.34)
            }, -- coordinates for food Station
            anim = {
                dict = "mp_ped_interaction",
                clip = "handshake_guy_a",
            },
            items = {
                [1] = {
                    item = "catcoffee",
                    ingredients = { ['boba'] = 1, ['milk'] = 1, ['sugar'] = 1 }
                },
                [2] = {
                    item = "nekolatte",
                    ingredients = { ['boba'] = 1, ['milk'] = 1 }
                },
                -- [3] = {
                --     item = "coffee",
                --     ingredients = { ['boba'] = 1, ['milk'] = 1 }
                -- },
                -- [4] = {
                --     item = "bobatea",
                --     ingredients = { ['boba'] = 1, ['milk'] = 1, ['sugar'] = 1 }
                -- },
                -- [5] = {
                --     item = "gbobatea",
                --     ingredients = { ['boba'] = 1, ['milk'] = 1, ['strawberry'] = 1 }
                -- },
                -- [6] = {
                --     item = "obobatea",
                --     ingredients = { ['boba'] = 1, ['milk'] = 1, ['orange'] = 1 }
                -- },
                [3] = {
                    item = "pbobatea",
                    ingredients = { ['boba'] = 1, ['milk'] = 1, ['strawberry'] = 1 }
                },
                -- [8] = {
                --     item = "mocha",
                --     ingredients = { ['milk'] = 1, ['sugar'] = 1 }
                -- },
                [4] = {
                    item = "bbobatea",
                    ingredients = { ['rice'] = 1, ['flour'] = 1, ['onion'] = 1, }
                },
            },
        },
        -- ["hob"] = {
        --     label = "Use Hob",
        --     craftinglabel = 'Use Hob',
        --     craftingtime = '2000',
        --     coords = {
        --         vector3(-590.7366, -1056.5004, 22.365)
        --     }, -- coordinates for food Station
        --     anim = {
        --         dict = "mp_ped_interaction",
        --         clip = "handshake_guy_a",
        --     },
        --     items = {
        --         [1] = {
        --             item = "miso",
        --             ingredients =  { ['nori'] = 1, ['tofu'] = 1, ['onion'] = 1, }
        --         },
        --         -- [2] = {
        --         --     item = "ramen",
        --         --     ingredients = { ['noodles'] = 1, ['onion'] = 1, }
        --         -- },
        --         -- [3] = {
        --         --     item = "noodlebowl",
        --         --     ingredients = { ['noodles'] = 1, }
        --         -- },
        --     },
        -- },
        ["chopping"] = {
            label = "Use Chopping Board",
            craftinglabel = 'Chopping Ingredients',
            craftingtime = '2000',
            coords = {
                vector3(-590.5, -1063.08, 22.36)
            }, -- coordinates for food Station
            anim = {
                dict = "mp_ped_interaction",
                clip = "handshake_guy_a",
            },
            items = {
                [1] = {
                    item = "bmochi",
                    ingredients = { ['sugar'] = 1, ['flour'] = 1, ['blueberry'] = 1, }
                },
                -- [2] = {
                --     item = "gmochi",
                --     ingredients = { ['sugar'] = 1, ['flour'] = 1, ['mint'] = 1, }
                -- },
                -- [3] = {
                --     item = "omochi",
                --     ingredients = { ['sugar'] = 1, ['flour'] = 1, ['orange'] = 1, }
                -- },
                [2] = {
                    item = "pmochi",
                    ingredients = { ['sugar'] = 1, ['flour'] = 1, ['strawberry'] = 1, }
                },
                -- [5] = {
                --     item = "riceball",
                --     ingredients = { ['rice'] = 1, ['nori'] = 1, }
                -- },
                -- [6] = {
                --     item = "bento",
                --     ingredients = { ['rice'] = 1, ['nori'] = 1, ['tofu'] = 1, }
                -- },
                [3] = {
                    item = "purrito",
                    ingredients = { ['rice'] = 1, ['flour'] = 1, ['onion'] = 1, }
                },
                
            },
        }
    },
    ["shop"] = {
        name = "cat_cafe_shop",
        label = "Cat Cafe Shop",
        items = {
           --- { name = 'uwu_mysterybox', price = 0 },
            { name = 'sugar', price = 0 },
			{ name = 'flour', price = 0 },
			{ name = 'nori', price = 0 },
			{ name = 'tofu', price = 0 },
			{ name = 'onion', price = 0 },
			{ name = 'boba', price = 0 },
			{ name = 'mint', price = 0 },
            { name = 'orange', price = 0 },
            { name = 'strawberry', price = 0 },
            { name = 'blueberry', price = 0 },
            { name = 'rice', price = 0 },
            { name = 'sake', price = 0 },
            { name = 'noodles', price = 0 },
            { name = 'milk', price = 0 },
        },
        coords = vector4(-589.41, -1068.59, 22.34, 353.09),
        ped = {
            ped = 'a_m_y_smartcaspat_01',
            scenario = "WORLD_HUMAN_HANG_OUT_STREET_CLUBHOUSE",
        },
        target = {
            radius = 1.5,
            targetIcon = "fas fa-wine-bottle",
            targetLabel = "Ingredients Shop",
            canInteract = function()
                return true
            end,
        }
    }
}