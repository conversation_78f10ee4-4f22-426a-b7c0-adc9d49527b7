local ESX = exports['es_extended']:getSharedObject()
local onDuty = false
local PlayerJob = {}
local jobLoaded = false
local dutyZones = {}
local zoneCheckThread = nil

local function IsPlayerOnDuty(jobName)
    if not jobName then return false end
    local prefix = "^" .. Config.DutySystem.offDutyPrefix 
    return not string.match(jobName, prefix)
end

local function GetBaseJobName(jobName)
    if not jobName then return nil end
    local prefix = "^" .. Config.DutySystem.offDutyPrefix
    if string.match(jobName, prefix) then
        return string.sub(jobName, string.len(Config.DutySystem.offDutyPrefix) + 1) 
    end
    return jobName
end

local function IsPlayerInAnyJobZone()
    local baseJobName = GetBaseJobName(PlayerJob.name)
    if not baseJobName or not dutyZones[baseJobName] then
        return false
    end
    
    local playerCoords = GetEntityCoords(PlayerPedId())
    return dutyZones[baseJobName]:isPointInside(playerCoords)
end

local function StartZoneCheckThread()
    if not Config.DutySystem.enabled or not Config.DutySystem.periodicZoneCheck.enabled then 
        return 
    end
    
    if zoneCheckThread then return end
    
    zoneCheckThread = CreateThread(function()
        while true do
            Wait(Config.DutySystem.periodicZoneCheck.interval * 1000) 
            
            if jobLoaded and onDuty and PlayerJob.name then
                local baseJobName = GetBaseJobName(PlayerJob.name)
                
                if baseJobName and dutyZones[baseJobName] then
                    if not IsPlayerInAnyJobZone() then
                        TriggerServerEvent("nukexd_businesses:toggleDuty", baseJobName)
                        if Config.DutySystem.periodicZoneCheck.triggerDutyLogger then
                            TriggerServerEvent("esx_dutylogger:ToggleDuty")
                        end
                        if Config.DutySystem.periodicZoneCheck.showNotification then
                            ESX.ShowNotification(Config.DutySystem.periodicZoneCheck.notificationMessage or "You have been automatically clocked off for leaving the work zone.")
                        end
                    end
                end
            end
        end
    end)
end

local function StopZoneCheckThread()
    if zoneCheckThread then
        zoneCheckThread = nil
    end
end

RegisterNetEvent('esx:playerLoaded', function()
    CreateThread(function()
        while ESX.GetPlayerData().job == nil do
            Wait(100)
        end
        PlayerJob = ESX.GetPlayerData().job
        onDuty = IsPlayerOnDuty(PlayerJob.name)
        jobLoaded = true
        InitializeDutyZones()
        StartZoneCheckThread()
    end)
end)

RegisterNetEvent('esx:setJob', function(JobInfo)
    PlayerJob = JobInfo
    onDuty = IsPlayerOnDuty(PlayerJob.name)
    if not jobLoaded then
        jobLoaded = true
        InitializeDutyZones()
        StartZoneCheckThread()
    end
end)

RegisterNetEvent('nukexd_businesses:dutyChanged', function(dutyStatus, newJobName)
    onDuty = dutyStatus
end)

function InitializeDutyZones()
    if not Config.DutySystem.enabled then return end

    for _, business in pairs(Businesses) do
        if business.enabled and business.dutyzone then
            local JobLocation = PolyZone:Create(business.dutyzone.zones, {
                name = business.job,
                debugPoly = Config.Debug
            })

            dutyZones[business.job] = JobLocation

            JobLocation:onPlayerInOut(function(isPointInside)
                local baseJobName = GetBaseJobName(PlayerJob.name)

                if baseJobName == business.job then
                    if business.dutyzone.autoClock.enter then
                        if isPointInside and not onDuty then
                            TriggerServerEvent("nukexd_businesses:toggleDuty", business.job)
                            TriggerServerEvent("esx_dutylogger:ToggleDuty")
                        end
                    end

                    if business.dutyzone.autoClock.exit then
                        if not isPointInside and onDuty then
                            TriggerServerEvent("nukexd_businesses:toggleDuty", business.job)
                            TriggerServerEvent("esx_dutylogger:ToggleDuty")
                        end
                    end
                end
            end)
        end
    end
end

-- Handles resource restarts
AddEventHandler('onClientResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        Wait(500)
        if ESX.IsPlayerLoaded() then
            PlayerJob = ESX.GetPlayerData().job
            onDuty = IsPlayerOnDuty(PlayerJob.name)
            jobLoaded = true
            InitializeDutyZones()
            StartZoneCheckThread()
        end
    end
end)

AddEventHandler('onClientResourceStop', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        StopZoneCheckThread()
    end
end)