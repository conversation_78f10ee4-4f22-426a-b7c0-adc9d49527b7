local ESX = exports['es_extended']:getSharedObject()
local onDuty = false
local PlayerJob = {}

RegisterNetEvent('esx:playerLoaded', function()
    local PlayerData = ESX.GetPlayerData()
    PlayerJob = PlayerData.job
    InitializeDutyZones()
end)

RegisterNetEvent('esx:setJob', function(JobInfo)
    PlayerJob = JobInfo
    onDuty = PlayerJob and PlayerJob.name
end)

RegisterNetEvent('esx_dutylogger:Client:SetDuty', function(duty)
    onDuty = duty
end)

function InitializeDutyZones()
    for k, i in pairs(Businesses) do
        local loc = i
        if i.enabled and loc.enabled then
            JobLocation = PolyZone:Create(loc.dutyzone.zones, {
                name = loc.job,
                debugPoly = Config.Debug
            })
            JobLocation:onPlayerInOut(function(isPointInside)
                if PlayerJob.name == loc.job then
                    if loc.dutyzone.autoClock.enter then
                        if isPointInside and not onDuty then
                            TriggerServerEvent("esx_dutylogger:ToggleDuty")
                        end
                    end

                    if loc.dutyzone.autoClock.exit then
                        if not isPointInside and onDuty then
                            TriggerServerEvent("esx_dutylogger:ToggleDuty")
                        end
                    end
                end
            end)
        end
    end
end

AddEventHandler('onClientResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        Wait(1000)
        while ESX.GetPlayerData().job == nil do
            Wait(100)
        end
        PlayerJob = ESX.GetPlayerData().job
        InitializeDutyZones()
    end
end)
