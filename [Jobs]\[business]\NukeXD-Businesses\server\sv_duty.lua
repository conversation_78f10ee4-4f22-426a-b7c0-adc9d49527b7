local ESX = exports['es_extended']:getSharedObject()

RegisterNetEvent('nukexd_businesses:toggleDuty', function(jobName)
    if not Config.DutySystem.enabled then return end

    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    local currentJob = xPlayer.getJob()
    local newJobName = nil
    local newJobGrade = currentJob.grade
    local offDutyJobName = Config.DutySystem.offDutyPrefix .. jobName

    if currentJob.name == jobName then
        newJobName = offDutyJobName
    elseif currentJob.name == offDutyJobName then
        newJobName = jobName
    else
        TriggerClientEvent('esx:showNotification', source, Config.DutySystem.notifications.noPermission)
        return
    end

    xPlayer.setJob(newJobName, newJobGrade)

    local isOnDuty = (newJobName == jobName)
    TriggerClientEvent('nukexd_businesses:dutyChanged', source, isOnDuty, newJobName)

    if isOnDuty then
        local message = string.format(Config.DutySystem.notifications.onDuty, jobName)
        TriggerClientEvent('esx:showNotification', source, message)
    else
        TriggerClientEvent('esx:showNotification', source, Config.DutySystem.notifications.offDuty)
    end
end)

function DoesJobExist(jobName)
    local result = MySQL.Sync.fetchAll('SELECT name FROM jobs WHERE name = @jobName', {
        ['@jobName'] = jobName
    })
    return #result > 0
end
