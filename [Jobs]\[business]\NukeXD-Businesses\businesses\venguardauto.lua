--- Bars & Foods

Businesses["venguardautos"] = {
    enabled = true,
    drawmarker = false,
    job = "venguardautos",
    dutyzone = {
        zones = {
            vector2(-537.88, 74.24),
            vector2(-486.36, 69.70),
            vector2(-492.80, 13.26),
            vector2(-540.91, 17.42),
            vector2(-542.80, 25.76)
        },
        autoClock = { enter = true, exit = true },
        label = "VenguardAutos",
    },
    blips = {
        enabled = false,
    },
    traysenabled = false,
    trays = {
        -- ["pharmacyTray2"] = {
        --     label = "Pharmacy Tray 2",
        --     slots = '20',
        --     weight = '20000',
        --     coords = {
        --         vector3(-591.56, -286.35, 35.73)
        --     },
        --     size = vec3(1.5, 1.5, 1.5),
        --     rotation = 5,
        --     icon = 'fa-solid fa-bowl-food',
        -- }
    },
    stash = {
        enabled = false,
        label = '',
        slots = '100',
        weight = '1000000',
        coords = {},
    },
     props = {
        enabled = false,
        prop = '',
        coords = {},
    },
    crafting = {},
}