--- Bars & Foods

Businesses["mommymm"] = {
    enabled = true,
    drawmarker = false,
    job = "mommymm",
    dutyzone = {
        zones = {
            vector2(-1619.70, -992.42),
            vector2(-1591.29, -1017.80),
            vector2(-1560.98, -982.95),
            vector2(-1589.77, -959.09)
        },
        autoClock = { enter = true, exit = true },
        label = "MonnyMM",
    },
    blips = {
        enabled = false,
    },
    traysenabled = false,
    trays = {
        -- ["pharmacyTray2"] = {
        --     label = "Pharmacy Tray 2",
        --     slots = '20',
        --     weight = '20000',
        --     coords = {
        --         vector3(-591.56, -286.35, 35.73)
        --     },
        --     size = vec3(1.5, 1.5, 1.5),
        --     rotation = 5,
        --     icon = 'fa-solid fa-bowl-food',
        -- }
    },
    stash = {
        enabled = false,
        label = '',
        slots = '100',
        weight = '1000000',
        coords = {},
    },
     props = {
        enabled = false,
        prop = '',
        coords = {},
    },
    crafting = {},
}