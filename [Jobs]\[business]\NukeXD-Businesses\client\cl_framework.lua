PlayerJob = {}
onDuty = false

ESX = exports['es_extended']:getSharedObject()

-- Shitty things buts it works
local function IsPlayerOnDuty(jobName)
    if not jobName then return false end
    local prefix = "^" .. Config.DutySystem.offDutyPrefix
    return not string.match(jobName, prefix)
end

RegisterNetEvent('esx:setJob', function(job)
    PlayerJob = job
    onDuty = IsPlayerOnDuty(PlayerJob.name)
end)

RegisterNetEvent('nukexd_businesses:dutyChanged', function(dutyStatus, newJobName)
    onDuty = dutyStatus
end)

AddEventHandler("onResourceStart", function(name)
    Wait(1000)
    if name == GetCurrentResourceName() then
        PlayerJob = ESX.GetPlayerData().job
        onDuty = IsPlayerOnDuty(PlayerJob.name)
    end
end)