PlayerJob = {}
onDuty = false

ESX = exports['es_extended']:getSharedObject()

RegisterNetEvent('esx:playerLoaded', function()
    PlayerJob = ESX.GetPlayerData().job
    onDuty = PlayerJob and PlayerJob.name
end)

RegisterNetEvent('esx:setJob', function(job)
    PlayerJob = job
    onDuty = PlayerJob and PlayerJob.name
end)

-- Function to check if the player is in a duty zone

AddEventHandler("onResourceStart", function(name)
    Wait(1000)
    if name == GetCurrentResourceName() then
        PlayerJob = ESX.GetPlayerData().job
        onDuty = PlayerJob and PlayerJob.name
    end
end)