shared_scripts { '@FiniAC/fini_events.lua' }


fx_version 'cerulean'
game 'gta5'

description 'Business System'
version '1.0.0'
author 'Snipe'

lua54 'yes'

shared_scripts{
    '@ox_lib/init.lua',
    'shared/**/*.lua',
    'businesses/*.lua'
}

client_scripts{
    '@PolyZone/client.lua',
    '@PolyZone/BoxZone.lua',
    '@PolyZone/EntityZone.lua',
    '@PolyZone/CircleZone.lua',
    '@PolyZone/ComboZone.lua',
    'client/**/*.lua',
} 

server_scripts{
    'server/**/*.lua',
}

dependency '/assetpacks'
